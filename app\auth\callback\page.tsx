'use client';

import { useEffect, useState } from 'react';
import { supabase } from '../../../lib/supabase';
import { useAuth } from '../../context/AuthContext';

export default function AuthCallback() {
  const { updateUserEmail } = useAuth();
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          setRedirectPath('/?error=auth_failed');
          return;
        }

        if (data.session?.user) {
          // Update auth context with user email
          updateUserEmail(data.session.user.email || '');

          // Redirect to appropriate dashboard based on user role
          const userRole = data.session.user.user_metadata?.role || 'master';
          setRedirectPath(userRole === 'master' ? '/master/dashboard' : '/child/dashboard');
        } else {
          setRedirectPath('/');
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        setRedirectPath('/?error=auth_failed');
      }
    };

    handleAuthCallback();
  }, [updateUserEmail]);

  // Handle redirect
  useEffect(() => {
    if (redirectPath) {
      window.location.href = redirectPath;
    }
  }, [redirectPath]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-muted-foreground">Completing authentication...</p>
      </div>
    </div>
  );
}


