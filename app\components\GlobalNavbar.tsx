'use client';

import Link from 'next/link';
import { Button } from '../../components/ui/button';
import { TrendingUp, Menu, X } from 'lucide-react';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { ThemeToggle } from './theme-toggle';
import LoginModal from './auth/LoginModal';
import { useAuth } from '../context/AuthContext';
import ProfileButton from './ProfileButton';


const navigation = [
  { name: 'Features', href: '#features' },
  { name: 'Pricing', href: '#pricing' },
  { name: 'FAQ', href: '#faq' },
  { name: 'Support', href: '/support' },
];

export default function GlobalNavbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const { isAuthenticated, user, logout } = useAuth();

  // Handle smooth scrolling for anchor links
  const handleAnchorClick = (href: string) => {
    console.log('Anchor click:', href); // Debug log
    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      console.log('Element found:', element); // Debug log
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  };

  return (
    <>
      {/* Sticky navbar with gaps all around */}
      <motion.div
        className="fixed top-4 left-4 right-4 z-50"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <nav
          className="bg-background/80 backdrop-blur-md border border-border/50 rounded-2xl shadow-lg shadow-black/5 dark:shadow-black/20 px-6 py-4 hover:shadow-xl hover:shadow-black/10 dark:hover:shadow-black/30 transition-all duration-300"
          aria-label="Global"
        >
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex lg:flex-1">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link href="/" className="flex items-center space-x-2">
                  <TrendingUp className="w-8 h-8 text-blue-600" />
                  <span className="text-xl font-bold text-foreground">CopyTrade</span>
                </Link>
              </motion.div>
            </div>

            {/* Mobile menu button */}
            <div className="flex lg:hidden">
              <button
                type="button"
                className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-muted-foreground hover:text-foreground"
                onClick={() => setMobileMenuOpen(true)}
              >
                <span className="sr-only">Open main menu</span>
                <Menu className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>

            {/* Desktop navigation */}
            <div className="hidden lg:flex lg:gap-x-8">
              {isAuthenticated ? (
                // Authenticated navigation
                <>
                  {user?.role === 'master' ? (
                    <>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Link
                          href="/master/dashboard"
                          className="text-sm font-semibold leading-6 text-foreground hover:text-blue-600 transition-colors"
                        >
                          Dashboard
                        </Link>
                      </motion.div>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Link
                          href="/master/configuration"
                          className="text-sm font-semibold leading-6 text-foreground hover:text-blue-600 transition-colors"
                        >
                          Configuration
                        </Link>
                      </motion.div>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Link
                          href="/demo/trading"
                          className="text-sm font-semibold leading-6 text-foreground hover:text-blue-600 transition-colors"
                        >
                          Demo Trade
                        </Link>
                      </motion.div>
                    </>
                  ) : (
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Link
                        href="/child/dashboard"
                        className="text-sm font-semibold leading-6 text-foreground hover:text-blue-600 transition-colors"
                      >
                        Dashboard
                      </Link>
                    </motion.div>
                  )}
                </>
              ) : (
                // Public navigation
                navigation.map((item) => (
                  <motion.div
                    key={item.name}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {item.href.startsWith('#') ? (
                      <button
                        onClick={() => handleAnchorClick(item.href)}
                        className="text-sm font-semibold leading-6 text-foreground hover:text-blue-600 transition-colors"
                      >
                        {item.name}
                      </button>
                    ) : (
                      <Link
                        href={item.href}
                        className="text-sm font-semibold leading-6 text-foreground hover:text-blue-600 transition-colors"
                      >
                        {item.name}
                      </Link>
                    )}
                  </motion.div>
                ))
              )}
            </div>

            {/* Right side actions */}
            <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4 lg:items-center">
              <ThemeToggle />
              {isAuthenticated ? (
                <ProfileButton />
              ) : (
                <>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        console.log('Login button clicked'); // Debug log
                        setLoginModalOpen(true);
                      }}
                    >
                      Log in
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      size="sm"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 dark:from-blue-500 dark:to-purple-500 dark:hover:from-blue-600 dark:hover:to-purple-600 text-white"
                      onClick={() => {
                        console.log('Sign up button clicked'); // Debug log
                        setLoginModalOpen(true);
                      }}
                    >
                      Sign up
                    </Button>
                  </motion.div>
                </>
              )}
            </div>
          </div>
        </nav>
      </motion.div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <motion.div
          className="lg:hidden fixed inset-0 z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="fixed inset-0 bg-black/20 backdrop-blur-sm" onClick={() => setMobileMenuOpen(false)} />
          <motion.div
            className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-background/95 backdrop-blur-md px-6 py-6 sm:max-w-sm border-l border-border/50 sm:rounded-l-2xl"
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-2">
                <TrendingUp className="w-8 h-8 text-blue-600" />
                <span className="text-xl font-bold text-foreground">CopyTrade</span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-muted-foreground hover:text-foreground"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <X className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-border/20">
                <div className="space-y-2 py-6">
                  {isAuthenticated ? (
                    // Authenticated mobile navigation
                    <>
                      {user?.role === 'master' ? (
                        <>
                          <Link
                            href="/master/dashboard"
                            className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Dashboard
                          </Link>
                          <Link
                            href="/master/configuration"
                            className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Configuration
                          </Link>
                          <Link
                            href="/demo/trading"
                            className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Demo Trade
                          </Link>
                        </>
                      ) : (
                        <Link
                          href="/child/dashboard"
                          className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          Dashboard
                        </Link>
                      )}
                    </>
                  ) : (
                    // Public mobile navigation
                    navigation.map((item) => (
                      item.href.startsWith('#') ? (
                        <button
                          key={item.name}
                          onClick={() => {
                            setMobileMenuOpen(false);
                            handleAnchorClick(item.href);
                          }}
                          className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted w-full text-left"
                        >
                          {item.name}
                        </button>
                      ) : (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-foreground hover:bg-muted"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      )
                    ))
                  )}
                </div>
                <div className="py-6">
                  {isAuthenticated ? (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Welcome, {user?.name || user?.email}
                      </p>
                      <button
                        className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-foreground hover:bg-muted w-full text-left"
                        onClick={() => {
                          setMobileMenuOpen(false);
                          logout();
                        }}
                      >
                        Log out
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <button
                        className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-foreground hover:bg-muted w-full text-left"
                        onClick={() => {
                          setMobileMenuOpen(false);
                          setLoginModalOpen(true);
                        }}
                      >
                        Log in
                      </button>
                      <button
                        className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-foreground hover:bg-muted w-full text-left"
                        onClick={() => {
                          setMobileMenuOpen(false);
                          setLoginModalOpen(true);
                        }}
                      >
                        Sign up
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Login Modal */}
      <LoginModal
        isOpen={loginModalOpen}
        onClose={() => setLoginModalOpen(false)}
      />
    </>
  );
}
