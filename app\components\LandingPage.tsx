'use client';

import { useAuth } from '../context/AuthContext';
import { useEffect, useState } from 'react';
import HeroSection from './landing/HeroSection';
import FeaturesSection from './landing/FeaturesSection';
import TestimonialsSection from './landing/TestimonialsSection';
import PricingSection from './landing/PricingSection';
import FAQSection from './landing/FAQSection';
import Footer from './landing/Footer';

export default function LandingPage() {
  const { isAuthenticated, user, loading } = useAuth();
  const [shouldRedirect, setShouldRedirect] = useState<string | null>(null);

  // If user is authenticated, redirect to appropriate dashboard
  useEffect(() => {
    if (isAuthenticated && user && !loading) {
      const dashboardPath = user.role === 'master' ? '/master/dashboard' : '/child/dashboard';
      setShouldRedirect(dashboardPath);
    }
  }, [isAuthenticated, user, loading]);

  // Handle redirect using window.location
  useEffect(() => {
    if (shouldRedirect) {
      window.location.href = shouldRedirect;
    }
  }, [shouldRedirect]);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      const dashboardPath = user?.role === 'master' ? '/master/dashboard' : '/child/dashboard';
      window.location.href = dashboardPath;
    } else {
      window.location.href = '/auth/register';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <HeroSection
        isAuthenticated={isAuthenticated}
        user={user}
        onGetStarted={handleGetStarted}
      />
      <div id="features">
        <FeaturesSection />
      </div>
      <div id="testimonials">
        <TestimonialsSection />
      </div>
      <div id="pricing">
        <PricingSection />
      </div>
      <div id="faq">
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
}
