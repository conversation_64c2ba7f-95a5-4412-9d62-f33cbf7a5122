'use client';

import { useAuth } from '../context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import HeroSection from './landing/HeroSection';
import FeaturesSection from './landing/FeaturesSection';
import TestimonialsSection from './landing/TestimonialsSection';
import PricingSection from './landing/PricingSection';
import FAQSection from './landing/FAQSection';
import Footer from './landing/Footer';

export default function LandingPage() {
  const { isAuthenticated, user, loading } = useAuth();
  const router = useRouter();

  // If user is authenticated, redirect to appropriate dashboard
  useEffect(() => {
    if (isAuthenticated && user && !loading) {
      const dashboardPath = user.role === 'master' ? '/master/dashboard' : '/child/dashboard';
      router.push(dashboardPath);
    }
  }, [isAuthenticated, user, loading, router]);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push(user?.role === 'master' ? '/master/dashboard' : '/child/dashboard');
    } else {
      router.push('/auth/register');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <HeroSection
        isAuthenticated={isAuthenticated}
        user={user}
        onGetStarted={handleGetStarted}
      />
      <div id="features">
        <FeaturesSection />
      </div>
      <div id="testimonials">
        <TestimonialsSection />
      </div>
      <div id="pricing">
        <PricingSection />
      </div>
      <div id="faq">
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
}
